from imports import *
from asyncpg import Record
from uuid import NAMESPACE_DNS, uuid5
from datetime import datetime

from managers.manager_postgreSQL import PostgreSQLManager
from managers.manager_retrieval import RetrievalManager
from managers.manager_qdrant import QDrantManager

class MeltanoManager:
    _instance = None
    _initialized = False

    def __new__(cls):
        if cls._instance is None:
            cls._instance = super().__new__(cls)
        return cls._instance

    @classmethod
    def get_instance(cls):
        return cls()

    @classmethod
    async def setup(cls):
        instance = cls.get_instance()
        if instance._initialized:
            return
        
        instance._initialized = True

    @classmethod
    async def ConvertFilesToVectorStore(cls, folder_path):
        from os import listdir, path, remove
        from unstructured.partition.auto import partition

        for filename in listdir(folder_path):
            file_path = path.join(folder_path, filename)
            if path.isfile(file_path):
                # Use Unstructured to parse the file
                unstructured = partition(filename=file_path)
                elements = await RetrievalManager.unstructured_to_list(unstructured)
                original_data = await RetrievalManager.list_to_dict(elements)
                markdown_content = await RetrievalManager.elements_to_markdown(data=elements, prefix="# File Data\n\n")
                embedded_dense = await RetrievalManager.get_embeddings_dense(markdown_content)
                embedded_sparse = await RetrievalManager.get_embeddings_sparse(markdown_content)
                id = str(uuid5(NAMESPACE_DNS, filename))
                await QDrantManager.upsert(id=id, text_dense=embedded_dense, text_sparse=embedded_sparse, data_as_str=markdown_content)
                remove(file_path)
                print(f"Converted {file_path} to the Vector Store. File has been deleted.")

    @classmethod
    async def ConvertSQLToVectorStore(cls):
        # Connect to the database using a direct connection
        await PostgreSQLManager.connect_to_database("meltanodb")
        # Get the table names
        table_names = await PostgreSQLManager.get_table_names("meltanodb")
        # Execute a query to fetch data
        for table_name in table_names:
            raw_data = await PostgreSQLManager.execute_query("meltanodb", "SELECT * FROM public." + table_name)
            #ids, jsons = zip(*raw_data)
            for i, json_data in enumerate(raw_data):
                record: Record = json_data
                # json_to_elements returns key/value pair. get_embeddings expects a str, List[str] or List[List[str]], so we convert to markdown first
                elements = dict(record)
                markdown_content = await RetrievalManager.elements_to_markdown(data=elements, prefix="# JSON Data\n\n")
                embedded_dense = await RetrievalManager.get_embeddings_dense(markdown_content)
                embedded_sparse = await RetrievalManager.get_embeddings_sparse(markdown_content)
                id = str(uuid5(NAMESPACE_DNS, next(record.values())))
                await QDrantManager.upsert(id=id, text_dense=embedded_dense, text_sparse=embedded_sparse, data_as_str=markdown_content)
        await PostgreSQLManager.close_connection("meltanodb")
