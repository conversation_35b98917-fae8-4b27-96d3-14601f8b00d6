from imports import *

import json
import onnxruntime
import numpy as np
from llama_index.embeddings.huggingface_optimum import OptimumEmbedding
from transformers import AutoTokenizer
from langchain_text_splitters import RecursiveCharacterTextSplitter
from typing import List, Union

class RetrievalManager:
    _instance = None
    _initialized = False

    session: onnxruntime.InferenceSession = None
    tokenizer: AutoTokenizer = None

    def __new__(cls):
        if cls._instance is None:
            cls._instance = super().__new__(cls)
        return cls._instance

    @classmethod
    def get_instance(cls):
        return cls()

    @classmethod
    async def setup(cls):
        instance = cls.get_instance()
        if instance._initialized:
            return

        # Load the ONNX model (adjust the path as necessary)
        onnx_model_path = BASE_DIR() / "bge_onnx" / "model.onnx"
        instance.session = onnxruntime.InferenceSession(onnx_model_path)
        # Load the tokenizer (adjust model name if different)
        instance.tokenizer = AutoTokenizer.from_pretrained("BAAI/bge-small-en")

        instance._initialized = True

    # Convert JSON to a list of elements (simplified for Markdown)
    @classmethod
    async def json_to_dict(cls, data, parent_key=""):
        elements = []
        if isinstance(data, str):
            elements.extend(await cls.json_to_dict(json.loads(data)))
        if isinstance(data, dict):
            for key, value in data.items():
                new_key = f"{parent_key}.{key}" if parent_key else key
                if isinstance(value, (dict, list)):
                    elements.extend(await cls.json_to_dict(value, new_key))
                else:
                    elements.append({"id": new_key, "text": value})
        elif isinstance(data, list):
            for i, item in enumerate(data):
                new_key = f"{parent_key}[{i}]"
                if isinstance(item, (dict, list)):
                    elements.extend(await cls.json_to_dict(item, new_key))
                else:
                    elements.append({"id": new_key, "text": item})
        return elements

    @classmethod
    async def unstructured_to_list(cls, elements):
        ret_val = []
        for element in elements:
            ret_val.append({
                "type": type(element).__name__,
                "id": getattr(element, "id", None),
                "text": str(element),
                "metadata": element.metadata.to_dict() if hasattr(element.metadata, "to_dict") else str(element.metadata),
            })
        return ret_val

    @classmethod
    def safe_get(cls, obj, key, default=None):
        try:
            if isinstance(obj, dict):
                return obj.get(key, default)
            return getattr(obj, key, default)
        except Exception:
            return default

    @classmethod
    async def list_to_dict(cls, mylist):
        ret_val = {}
        for item in mylist:
            ret_val[cls.safe_get(item,'id')] = cls.safe_get(item,'text')
        return ret_val

    @classmethod
    async def elements_to_markdown(cls, data, prefix=""):
        markdown_content = prefix
        def iteration(ret_val, my_data):
            for element in my_data:
                ret_val += f"**{element}**: {cls.safe_get(my_data, element)}\n\n"
            return ret_val
        if isinstance(data, list):
            for item in data:
                markdown_content = iteration(ret_val=markdown_content,my_data=item)
        else:
            markdown_content = iteration(ret_val=markdown_content,my_data=data)
        return markdown_content

    @classmethod
    async def get_embeddings_dense(cls, text):
        instance = cls.get_instance()

        """
        Generate embeddings using the ONNX model with tokenized inputs.
        Compatible with OpenAI's input expectations.
        """
        # Handle single string or list of strings (OpenAI accepts both)
        if isinstance(text, str):
            texts = [text]
        elif isinstance(text, list):
            texts = text
        else:
            raise ValueError("Input must be a string or list of strings")

        # Tokenize the input (assuming apiendpoint.tokenizer is available)
        inputs = instance.tokenizer(
            texts,
            padding=True,
            truncation=True,
            return_tensors="np",  # Return NumPy arrays for ONNX
            max_length=EMBEDDING_SIZE  # Matches common OpenAI defaults, adjust if needed
        )

        # Prepare input feed for ONNX, casting to int64
        input_feed = {
            "input_ids": inputs["input_ids"].astype(np.int64),
            "attention_mask": inputs["attention_mask"].astype(np.int64),
            "token_type_ids": inputs.get("token_type_ids", np.zeros_like(inputs["input_ids"])).astype(np.int64)
        }

        # Run the ONNX model (assuming apiendpoint.session is your ONNX session)
        output1 = instance.session.run(None, input_feed)

        # Alternative embedding generation (assuming Settings.embed_model exists)
        # If this is redundant, remove it and use output1[0] directly
        embeddings = [ZairaSettings.OllamaSettings().embed_model.get_text_embedding(t) for t in texts]

        # Convert embeddings to list if they are NumPy arrays (OpenAI expects JSON-serializable output)
        embeddings = [e.tolist() if isinstance(e, np.ndarray) else e for e in embeddings]

        return embeddings[0]

    @classmethod
    async def get_embeddings_sparse(cls, text):
        return list(ZairaSettings.sparse_embed_model.embed(text))[0]

    @classmethod
    async def chunk_text(cls, data: Union[str, List[str]], chunk_size: int = 1000, chunk_overlap: int = 200) -> List[str]:
        """
        Split text into chunks using LangChain's RecursiveCharacterTextSplitter.

        This method implements text-structured based chunking that:
        - Attempts to keep larger units (e.g., paragraphs) intact
        - If a unit exceeds the chunk size, it moves to the next level (e.g., sentences)
        - This process continues down to the word level if necessary
        - Maintains natural language flow and semantic coherence within chunks
        - Adapts to varying levels of text granularity

        Args:
            data: Input text as string or list of strings
            chunk_size: Maximum size of each chunk (default: 1000)
            chunk_overlap: Number of characters to overlap between chunks (default: 200)

        Returns:
            List of text chunks
        """
        # Initialize the RecursiveCharacterTextSplitter
        text_splitter = RecursiveCharacterTextSplitter(
            chunk_size=chunk_size,
            chunk_overlap=chunk_overlap,
            length_function=len,
            is_separator_regex=False,
        )

        # Handle different input types
        if isinstance(data, str):
            # Single string input
            chunks = text_splitter.split_text(data)
        elif isinstance(data, list):
            # List of strings input - concatenate and split
            combined_text = "\n\n".join(str(item) for item in data)
            chunks = text_splitter.split_text(combined_text)
        else:
            # Convert other types to string
            text_data = str(data)
            chunks = text_splitter.split_text(text_data)

        return chunks
