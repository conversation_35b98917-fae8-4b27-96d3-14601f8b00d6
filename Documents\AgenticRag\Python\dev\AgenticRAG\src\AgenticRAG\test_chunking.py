#!/usr/bin/env python3
"""
Test script for the new chunking functionality using LangChain's RecursiveCharacterTextSplitter.
"""

import asyncio
from managers.manager_retrieval import RetrievalManager

async def test_chunking():
    """Test the new chunk_text method."""
    
    # Sample markdown content to test chunking
    sample_text = """
# Document Title

This is a sample document that we want to test with our new chunking functionality.

## Section 1: Introduction

Lorem ipsum dolor sit amet, consectetur adipiscing elit. Sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris nisi ut aliquip ex ea commodo consequat.

Duis aute irure dolor in reprehenderit in voluptate velit esse cillum dolore eu fugiat nulla pariatur. Excepteur sint occaecat cupidatat non proident, sunt in culpa qui officia deserunt mollit anim id est laborum.

## Section 2: Main Content

Sed ut perspiciatis unde omnis iste natus error sit voluptatem accusantium doloremque laudantium, totam rem aperiam, eaque ipsa quae ab illo inventore veritatis et quasi architecto beatae vitae dicta sunt explicabo.

Nemo enim ipsam voluptatem quia voluptas sit aspernatur aut odit aut fugit, sed quia consequuntur magni dolores eos qui ratione voluptatem sequi nesciunt.

### Subsection 2.1

Neque porro quisquam est, qui dolorem ipsum quia dolor sit amet, consectetur, adipisci velit, sed quia non numquam eius modi tempora incidunt ut labore et dolore magnam aliquam quaerat voluptatem.

### Subsection 2.2

Ut enim ad minima veniam, quis nostrum exercitationem ullam corporis suscipit laboriosam, nisi ut aliquid ex ea commodi consequatur? Quis autem vel eum iure reprehenderit qui in ea voluptate velit esse quam nihil molestiae consequatur.

## Section 3: Conclusion

At vero eos et accusamus et iusto odio dignissimos ducimus qui blanditiis praesentium voluptatum deleniti atque corrupti quos dolores et quas molestias excepturi sint occaecati cupiditate non provident.

Similique sunt in culpa qui officia deserunt mollitia animi, id est laborum et dolorum fuga. Et harum quidem rerum facilis est et expedita distinctio.
"""

    try:
        print("Testing chunking functionality...")
        print(f"Original text length: {len(sample_text)} characters")
        print("-" * 50)
        
        # Test with default parameters
        chunks = await RetrievalManager.chunk_text(sample_text)
        
        print(f"Number of chunks created: {len(chunks)}")
        print("-" * 50)
        
        for i, chunk in enumerate(chunks):
            print(f"Chunk {i + 1} (length: {len(chunk)} characters):")
            print(chunk[:200] + "..." if len(chunk) > 200 else chunk)
            print("-" * 30)
        
        # Test with smaller chunk size
        print("\nTesting with smaller chunk size (500 characters)...")
        small_chunks = await RetrievalManager.chunk_text(sample_text, chunk_size=500, chunk_overlap=50)
        
        print(f"Number of small chunks created: {len(small_chunks)}")
        print("-" * 50)
        
        for i, chunk in enumerate(small_chunks):
            print(f"Small Chunk {i + 1} (length: {len(chunk)} characters):")
            print(chunk[:150] + "..." if len(chunk) > 150 else chunk)
            print("-" * 30)
            
        # Test with list input
        print("\nTesting with list input...")
        list_input = [
            "First document content here.",
            "Second document with more content.",
            "Third document with even more content to test."
        ]
        
        list_chunks = await RetrievalManager.chunk_text(list_input, chunk_size=100)
        print(f"Number of chunks from list input: {len(list_chunks)}")
        
        for i, chunk in enumerate(list_chunks):
            print(f"List Chunk {i + 1}: {chunk}")
            
        print("\nChunking test completed successfully!")
        
    except Exception as e:
        print(f"Error during chunking test: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(test_chunking())
